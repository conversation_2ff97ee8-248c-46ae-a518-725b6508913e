import styled from '@emotion/styled';
import {ReactNode} from 'react';
import chatBg from '@/assets/mcp/chatBg.png';
import inputBg from '@/assets/mcp/inputBg.png';
import welcomeBg from '@/assets/mcp/welcomeBg.png';

const ChatContainer = styled.div<{showWelcome: boolean}>`
    height: 100%;
    width: 100%;
    position: relative;
    min-width: 930px;
    padding-bottom: 102px;
    overflow: hidden auto;
    background: url(${chatBg}) bottom / 100% auto no-repeat fixed;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: ${props => props.showWelcome ? `url(${welcomeBg}) top / 100% auto no-repeat fixed` : 'none'};
        pointer-events: none;
    }
`;

const MessagesArea = styled.div<{hasMessages: boolean}>`
    height: 100%;
    width: 100%;
    overflow: auto;
    scrollbar-width: none;
    min-width: 830px;
    max-width: 80%;
    margin: 0 auto;
    margin-left: ${props => props.hasMessages ? 'calc(50% - 40% - 50px)' : 'auto'};
`;

const InputArea = styled.div`
    width: 100%;
    padding: 12px 0;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: url(${inputBg}) bottom / 100% auto no-repeat;
    z-index: 10;

    > * {
        min-width: 830px;
        max-width: 80%;
        margin: 0 auto;
        display: block;
    }
`;

interface Props {
    messagePanel: ReactNode;
    inputPanel: ReactNode;
    hasMessages?: boolean;
}

export default function ChatLayout({
    messagePanel,
    inputPanel,
    hasMessages = false,
}: Props) {
    return (
        <ChatContainer showWelcome={!hasMessages}>
            <MessagesArea hasMessages={hasMessages}>
                {messagePanel}
            </MessagesArea>
            <InputArea>
                {inputPanel}
            </InputArea>
        </ChatContainer>
    );
}
