import styled from '@emotion/styled';
import {css} from '@emotion/react';
import {ReactNode} from 'react';
import chatBg from '@/assets/mcp/chatBg.png';
import inputBg from '@/assets/mcp/inputBg.png';
import welcomeBg from '@/assets/mcp/welcomeBg.png';

const fullSizeStyles = css`
    height: 100%;
    width: 100%;
`;

const responsiveContainerStyles = css`
    min-width: 830px;
    max-width: 80%;
    margin: 0 auto;
`;

const backgroundImageStyles = css`
    background-size: 100% auto;
    background-repeat: no-repeat;
    background-attachment: fixed;
`;

const Wrapper = styled.div`
    ${fullSizeStyles}
    background: url(${chatBg}) bottom / 100% auto no-repeat fixed;
    overflow: hidden auto;
`;

const Container = styled.div<{visible: boolean}>`
    ${fullSizeStyles}
    ${backgroundImageStyles}
    background-image: ${props =>
        (props.visible ? `url(${welcomeBg})` : 'none')};
    background-position: top;
    position: relative;
    min-width: 930px;
    padding-bottom: 102px;
`;

const MessagesContainer = styled.div`
    ${fullSizeStyles}
    overflow: auto;
    scrollbar-width: none;
`;

const InputContainer = styled.div`
    width: 100%;
    padding: 12px 0;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: url(${inputBg}) bottom / 100% auto no-repeat;
    z-index: 10;
`;

const ContentWrapper = styled.div`
    ${fullSizeStyles}
    ${responsiveContainerStyles}
`;

const MessagePanel = styled.div<{ hasMessages: boolean }>`
    margin-left: ${props => (props.hasMessages ? '-50px' : 0)};
`;

const InputPanel = styled.div`
    ${responsiveContainerStyles}
`;

interface Props {
    messagePanel: ReactNode;
    inputPanel: ReactNode;
    hasMessages?: boolean;
}

export default function ChatLayout({
    messagePanel,
    inputPanel,
    hasMessages = false,
}: Props) {
    return (
        <Wrapper>
            <Container visible={!hasMessages}>
                <MessagesContainer>
                    <ContentWrapper>
                        <MessagePanel hasMessages={hasMessages}>
                            {messagePanel}
                        </MessagePanel>
                    </ContentWrapper>
                </MessagesContainer>
                <InputContainer>
                    <InputPanel>{inputPanel}</InputPanel>
                </InputContainer>
            </Container>
        </Wrapper>
    );
}
